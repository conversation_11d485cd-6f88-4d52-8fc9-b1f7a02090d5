# MERN Authentication App

A full-stack MERN (MongoDB, Express.js, React, Node.js) authentication application with phone number verification via SMS OTP.

## 🔄 Authentication Flow

### New Improved Flow (OTP-First Signup):
1. **User fills signup form** → Form data collected
2. **OTP sent to phone** → User data stored temporarily (not in database)
3. **User verifies OTP** → User account created in database (already verified)
4. **User can login** → Standard email/password authentication

### Benefits:
- ✅ No unverified users in database
- ✅ Cleaner user experience
- ✅ Prevents spam registrations
- ✅ Phone verification before account creation

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB
- Twilio account (for SMS)
- Cloudinary account (for image uploads)

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Environment Configuration:**
   ```bash
   cp .env.example .env
   ```
   Then edit `.env` with your actual credentials.

4. **Start the server:**
   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm start
   ```

### Frontend Setup

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

## 📱 API Endpoints

### Authentication Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/auth/send-otp` | Send OTP for signup (with form data) |
| POST | `/api/auth/verify-otp-signup` | Verify OTP and create user account |
| POST | `/api/auth/login` | Login with email/password |

### Request/Response Examples

#### 1. Send OTP for Signup
```javascript
// POST /api/auth/send-otp
// Content-Type: multipart/form-data

FormData {
  fullName: "John Doe",
  email: "<EMAIL>",
  password: "securepassword",
  confirmPassword: "securepassword",
  phone: "+**********",
  photo: File // optional
}

// Response
{
  "message": "OTP sent to phone. Please verify to complete signup."
}
```

#### 2. Verify OTP and Create Account
```javascript
// POST /api/auth/verify-otp-signup
{
  "phone": "+**********",
  "otp": "123456"
}

// Response
{
  "message": "Account created successfully. You can now login."
}
```

#### 3. Login
```javascript
// POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "securepassword"
}

// Response
{
  "token": "jwt-token-here"
}
```

## 🛠️ Technology Stack

### Backend
- **Express.js** - Web framework
- **MongoDB** with Mongoose - Database
- **bcryptjs** - Password hashing
- **jsonwebtoken** - JWT authentication
- **Twilio** - SMS OTP service
- **Cloudinary** - Image storage
- **Multer** - File upload handling

### Frontend
- **React 19.1.1** - UI framework
- **React Router DOM** - Client-side routing
- **Axios** - HTTP client
- **Create React App** - Build tooling

## 📁 Project Structure

```
mern-auth-app/
├── backend/
│   ├── config/
│   │   ├── cloudinary.js      # Cloudinary configuration
│   │   └── firebase.js        # Firebase admin setup
│   ├── controllers/
│   │   └── authController.js  # Authentication logic
│   ├── middleware/
│   │   └── auth.js            # JWT middleware (empty)
│   ├── models/
│   │   ├── User.js            # User schema
│   │   └── Device.js          # Device schema
│   ├── routes/
│   │   └── authRoutes.js      # API routes
│   ├── utils/
│   │   └── otp.js             # OTP utilities
│   ├── .env.example           # Environment template
│   ├── index.js               # Server entry point
│   └── package.json
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── LoginPage.jsx
│   │   │   ├── SignupPage.jsx
│   │   │   └── OTPVerificationPage.jsx
│   │   ├── App.js             # Main routing
│   │   ├── App.css
│   │   └── index.js
│   └── package.json
└── README.md
```

## 🔧 Key Features

- **Phone Number Verification** - SMS OTP via Twilio
- **Secure Password Storage** - bcrypt hashing
- **JWT Authentication** - Stateless authentication
- **Image Upload** - Profile photos via Cloudinary
- **Responsive Design** - Mobile-friendly UI
- **Error Handling** - Comprehensive error messages
- **Loading States** - Better UX with loading indicators

## 🔒 Security Features

- Password confirmation validation
- Duplicate email/phone prevention
- OTP expiration (10 minutes)
- Temporary user data expiration (15 minutes)
- JWT token-based authentication
- Input validation and sanitization

## 🚀 Deployment

### Backend Deployment
1. Set up MongoDB Atlas or your preferred MongoDB hosting
2. Configure environment variables on your hosting platform
3. Deploy to Heroku, Railway, or your preferred platform

### Frontend Deployment
1. Build the React app: `npm run build`
2. Deploy to Netlify, Vercel, or your preferred platform
3. Update API base URL for production

## 📝 Environment Variables

Create a `.env` file in the backend directory with the following variables:

```env
MONGO_URI=your-mongodb-connection-string
PORT=5000
JWT_SECRET=your-jwt-secret-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
