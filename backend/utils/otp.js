const twilio = require('twilio');

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const twilioPhone = process.env.TWILIO_PHONE_NUMBER;

const client = twilio(accountSid, authToken);

// In-memory store for OTPs (for demo; use DB or cache for production)
const otpStore = {};

// In-memory store for pending user data during signup
const pendingUsersStore = {};

function generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

exports.sendOTP = (phone) => {
    const otp = generateOTP();
    otpStore[phone] = {
        otp: otp,
        timestamp: Date.now(),
        expires: Date.now() + (10 * 60 * 1000) // 10 minutes expiry
    };

    client.messages.create({
        body: `Your OTP code is ${otp}`,
        from: twilioPhone,
        to: phone
    }).catch(err => {
        console.error('Twilio error:', err);
    });

    return otp;
};

exports.verifyOTP = (phone, otp) => {
    const storedData = otpStore[phone];
    if (storedData && storedData.otp === otp && Date.now() < storedData.expires) {
        delete otpStore[phone];
        return true;
    }
    return false;
};

// Functions to manage pending user data
exports.storePendingUser = (phone, userData) => {
    pendingUsersStore[phone] = {
        ...userData,
        timestamp: Date.now(),
        expires: Date.now() + (15 * 60 * 1000) // 15 minutes expiry
    };
};

exports.getPendingUser = (phone) => {
    const userData = pendingUsersStore[phone];
    if (userData && Date.now() < userData.expires) {
        return userData;
    }
    // Clean up expired data
    if (userData) {
        delete pendingUsersStore[phone];
    }
    return null;
};

exports.deletePendingUser = (phone) => {
    delete pendingUsersStore[phone];
};