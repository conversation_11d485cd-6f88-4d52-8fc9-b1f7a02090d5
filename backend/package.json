{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "firebase-admin": "^13.4.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "twilio": "^5.8.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "description": ""}