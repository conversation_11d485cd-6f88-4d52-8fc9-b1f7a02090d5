const express = require('express');
const router = express.Router();
const { storage } = require('../config/cloudinary');
const multer = require('multer');
const upload = multer({ storage });
const authController = require('../controllers/authController');

// New flow: Send OTP first, then create user after verification
router.post('/send-otp', upload.single('photo'), authController.sendOTPForSignup);
router.post('/verify-otp-signup', authController.verifyOTPAndCreateUser);
router.post('/login', authController.login);

module.exports = router;