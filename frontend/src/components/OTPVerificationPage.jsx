import React, { useState } from 'react';
import axios from 'axios';

const OTPVerificationPage = ({ phone, onVerified }) => {
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Use the new endpoint that creates the user after OTP verification
      await axios.post('/api/auth/verify-otp-signup', { phone, otp });
      onVerified();
    } catch (err) {
      setError(err.response?.data?.error || 'Invalid OTP. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Verify Your Phone Number</h2>
      <p>We've sent a 6-digit code to {phone}</p>

      <form onSubmit={handleSubmit}>
        {error && <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>}

        <input
          value={otp}
          onChange={e => setOtp(e.target.value)}
          placeholder="Enter 6-digit OTP"
          maxLength="6"
          required
          disabled={loading}
        />
        <button type="submit" disabled={loading || otp.length !== 6}>
          {loading ? 'Verifying...' : 'Verify & Create Account'}
        </button>
      </form>
    </div>
  );
};

export default OTPVerificationPage;