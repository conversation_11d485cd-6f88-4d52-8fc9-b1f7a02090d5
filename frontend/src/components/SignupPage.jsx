import React, { useState } from 'react';
import axios from 'axios';
import './Auth.css';

const SignupPage = ({ onSignupSuccess }) => {
  const [form, setForm] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    photo: null,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = e => {
    const { name, value, files } = e.target;
    setForm(prev => ({
      ...prev,
      [name]: files ? files[0] : value,
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async e => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const data = new FormData();
      Object.entries(form).forEach(([key, value]) => data.append(key, value));

      // Send OTP first (doesn't create user yet)
      await axios.post('/api/auth/send-otp', data);

      // Navigate to OTP verification with form data
      onSignupSuccess(form.phone);
    } catch (err) {
      setError(err.response?.data?.error || 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>}

      <input
        name="fullName"
        placeholder="Full Name"
        onChange={handleChange}
        value={form.fullName}
        required
        disabled={loading}
      />
      <input
        name="email"
        type="email"
        placeholder="Email"
        onChange={handleChange}
        value={form.email}
        required
        disabled={loading}
      />
      <input
        name="password"
        type="password"
        placeholder="Password"
        onChange={handleChange}
        value={form.password}
        required
        disabled={loading}
      />
      <input
        name="confirmPassword"
        type="password"
        placeholder="Confirm Password"
        onChange={handleChange}
        value={form.confirmPassword}
        required
        disabled={loading}
      />
      <input
        name="phone"
        placeholder="Phone (e.g., +1234567890)"
        onChange={handleChange}
        value={form.phone}
        required
        disabled={loading}
      />
      <input
        name="photo"
        type="file"
        accept="image/*"
        onChange={handleChange}
        disabled={loading}
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Sending OTP...' : 'Send OTP'}
      </button>
    </form>
  );
};

export default SignupPage