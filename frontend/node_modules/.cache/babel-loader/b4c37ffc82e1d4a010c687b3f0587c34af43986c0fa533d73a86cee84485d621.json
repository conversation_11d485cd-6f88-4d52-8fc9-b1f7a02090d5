{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Task1/frontend/src/components/SignupPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignupPage = ({\n  onSignupSuccess\n}) => {\n  _s();\n  const [form, setForm] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    photo: null\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      files\n    } = e.target;\n    setForm(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const data = new FormData();\n      Object.entries(form).forEach(([key, value]) => data.append(key, value));\n\n      // Send OTP first (doesn't create user yet)\n      await axios.post('/api/auth/send-otp', data);\n\n      // Navigate to OTP verification with form data\n      onSignupSuccess(form.phone);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Something went wrong. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 py-12 px-4 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full space-y-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-extrabold text-gray-900 mb-2\",\n            children: \"Create Account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-600\",\n            children: \"Join us today and get started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"mt-8 space-y-6\",\n          onSubmit: handleSubmit,\n          children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"fullName\",\n                className: \"sr-only\",\n                children: \"Full Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"fullName\",\n                name: \"fullName\",\n                type: \"text\",\n                placeholder: \"Full Name\",\n                onChange: handleChange,\n                value: form.fullName,\n                required: true,\n                disabled: loading,\n                className: \"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"email\",\n                className: \"sr-only\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"email\",\n                name: \"email\",\n                type: \"email\",\n                placeholder: \"Email address\",\n                onChange: handleChange,\n                value: form.email,\n                required: true,\n                disabled: loading,\n                className: \"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"sr-only\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"password\",\n                name: \"password\",\n                type: \"password\",\n                placeholder: \"Password\",\n                onChange: handleChange,\n                value: form.password,\n                required: true,\n                disabled: loading,\n                className: \"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"confirmPassword\",\n                className: \"sr-only\",\n                children: \"Confirm Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                placeholder: \"Confirm Password\",\n                onChange: handleChange,\n                value: form.confirmPassword,\n                required: true,\n                disabled: loading,\n                className: \"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"phone\",\n                className: \"sr-only\",\n                children: \"Phone Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"phone\",\n                name: \"phone\",\n                type: \"tel\",\n                placeholder: \"Phone (e.g., +1234567890)\",\n                onChange: handleChange,\n                value: form.phone,\n                required: true,\n                disabled: loading,\n                className: \"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"photo\",\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Profile Photo (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"photo\",\n                name: \"photo\",\n                type: \"file\",\n                accept: \"image/*\",\n                onChange: handleChange,\n                disabled: loading,\n                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 ease-in-out\",\n              children: [loading && /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), loading ? 'Sending OTP...' : 'Send OTP']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(SignupPage, \"DW0XRrGBftvfDMfLEL4DehtAtUY=\");\n_c = SignupPage;\nexport default SignupPage;\nvar _c;\n$RefreshReg$(_c, \"SignupPage\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "SignupPage", "onSignupSuccess", "_s", "form", "setForm", "fullName", "email", "password", "confirmPassword", "phone", "photo", "loading", "setLoading", "error", "setError", "handleChange", "e", "name", "value", "files", "target", "prev", "handleSubmit", "preventDefault", "data", "FormData", "Object", "entries", "for<PERSON>ach", "key", "append", "post", "err", "_err$response", "_err$response$data", "response", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "type", "placeholder", "onChange", "required", "disabled", "accept", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Task1/frontend/src/components/SignupPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './Auth.css';\n\nconst SignupPage = ({ onSignupSuccess }) => {\n  const [form, setForm] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    photo: null,\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = e => {\n    const { name, value, files } = e.target;\n    setForm(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value,\n    }));\n  \n    if (error) setError('');\n  };\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const data = new FormData();\n      Object.entries(form).forEach(([key, value]) => data.append(key, value));\n\n      // Send OTP first (doesn't create user yet)\n      await axios.post('/api/auth/send-otp', data);\n\n      // Navigate to OTP verification with form data\n      onSignupSuccess(form.phone);\n    } catch (err) {\n      setError(err.response?.data?.error || 'Something went wrong. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"bg-white rounded-lg shadow-xl p-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-extrabold text-gray-900 mb-2\">Create Account</h2>\n            <p className=\"text-sm text-gray-600\">Join us today and get started</p>\n          </div>\n\n          <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"fullName\" className=\"sr-only\">Full Name</label>\n                <input\n                  id=\"fullName\"\n                  name=\"fullName\"\n                  type=\"text\"\n                  placeholder=\"Full Name\"\n                  onChange={handleChange}\n                  value={form.fullName}\n                  required\n                  disabled={loading}\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"sr-only\">Email</label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  placeholder=\"Email address\"\n                  onChange={handleChange}\n                  value={form.email}\n                  required\n                  disabled={loading}\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"sr-only\">Password</label>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  placeholder=\"Password\"\n                  onChange={handleChange}\n                  value={form.password}\n                  required\n                  disabled={loading}\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"sr-only\">Confirm Password</label>\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type=\"password\"\n                  placeholder=\"Confirm Password\"\n                  onChange={handleChange}\n                  value={form.confirmPassword}\n                  required\n                  disabled={loading}\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"phone\" className=\"sr-only\">Phone Number</label>\n                <input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  placeholder=\"Phone (e.g., +1234567890)\"\n                  onChange={handleChange}\n                  value={form.phone}\n                  required\n                  disabled={loading}\n                  className=\"appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm disabled:bg-gray-100 disabled:cursor-not-allowed\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"photo\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Profile Photo (Optional)\n                </label>\n                <input\n                  id=\"photo\"\n                  name=\"photo\"\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleChange}\n                  disabled={loading}\n                  className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition duration-150 ease-in-out\"\n              >\n                {loading && (\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                )}\n                {loading ? 'Sending OTP...' : 'Send OTP'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignupPage"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC;IAC/BS,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMmB,YAAY,GAAGC,CAAC,IAAI;IACxB,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IACvChB,OAAO,CAACiB,IAAI,KAAK;MACf,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD;IAC7B,CAAC,CAAC,CAAC;IAEH,IAAIL,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAMN,CAAC,IAAI;IAC9BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMU,IAAI,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC3BC,MAAM,CAACC,OAAO,CAACxB,IAAI,CAAC,CAACyB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEX,KAAK,CAAC,KAAKM,IAAI,CAACM,MAAM,CAACD,GAAG,EAAEX,KAAK,CAAC,CAAC;;MAEvE;MACA,MAAMrB,KAAK,CAACkC,IAAI,CAAC,oBAAoB,EAAEP,IAAI,CAAC;;MAE5C;MACAvB,eAAe,CAACE,IAAI,CAACM,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOuB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZpB,QAAQ,CAAC,EAAAmB,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcT,IAAI,cAAAU,kBAAA,uBAAlBA,kBAAA,CAAoBrB,KAAK,KAAI,yCAAyC,CAAC;IAClF,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IAAKqC,SAAS,EAAC,8HAA8H;IAAAC,QAAA,eAC3ItC,OAAA;MAAKqC,SAAS,EAAC,2BAA2B;MAAAC,QAAA,eACxCtC,OAAA;QAAKqC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDtC,OAAA;UAAKqC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtC,OAAA;YAAIqC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9E1C,OAAA;YAAGqC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eAEN1C,OAAA;UAAMqC,SAAS,EAAC,gBAAgB;UAACM,QAAQ,EAAEpB,YAAa;UAAAe,QAAA,GACrDxB,KAAK,iBACJd,OAAA;YAAKqC,SAAS,EAAC,2EAA2E;YAAAC,QAAA,EACvFxB;UAAK;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED1C,OAAA;YAAKqC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtC,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,UAAU;gBAACP,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/D1C,OAAA;gBACE6C,EAAE,EAAC,UAAU;gBACb3B,IAAI,EAAC,UAAU;gBACf4B,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,WAAW;gBACvBC,QAAQ,EAAEhC,YAAa;gBACvBG,KAAK,EAAEf,IAAI,CAACE,QAAS;gBACrB2C,QAAQ;gBACRC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAgQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxD1C,OAAA;gBACE6C,EAAE,EAAC,OAAO;gBACV3B,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,OAAO;gBACZC,WAAW,EAAC,eAAe;gBAC3BC,QAAQ,EAAEhC,YAAa;gBACvBG,KAAK,EAAEf,IAAI,CAACG,KAAM;gBAClB0C,QAAQ;gBACRC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAgQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,UAAU;gBAACP,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9D1C,OAAA;gBACE6C,EAAE,EAAC,UAAU;gBACb3B,IAAI,EAAC,UAAU;gBACf4B,IAAI,EAAC,UAAU;gBACfC,WAAW,EAAC,UAAU;gBACtBC,QAAQ,EAAEhC,YAAa;gBACvBG,KAAK,EAAEf,IAAI,CAACI,QAAS;gBACrByC,QAAQ;gBACRC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAgQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,iBAAiB;gBAACP,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC7E1C,OAAA;gBACE6C,EAAE,EAAC,iBAAiB;gBACpB3B,IAAI,EAAC,iBAAiB;gBACtB4B,IAAI,EAAC,UAAU;gBACfC,WAAW,EAAC,kBAAkB;gBAC9BC,QAAQ,EAAEhC,YAAa;gBACvBG,KAAK,EAAEf,IAAI,CAACK,eAAgB;gBAC5BwC,QAAQ;gBACRC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAgQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/D1C,OAAA;gBACE6C,EAAE,EAAC,OAAO;gBACV3B,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,KAAK;gBACVC,WAAW,EAAC,2BAA2B;gBACvCC,QAAQ,EAAEhC,YAAa;gBACvBG,KAAK,EAAEf,IAAI,CAACM,KAAM;gBAClBuC,QAAQ;gBACRC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAgQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3Q,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1C,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAO4C,OAAO,EAAC,OAAO;gBAACP,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1C,OAAA;gBACE6C,EAAE,EAAC,OAAO;gBACV3B,IAAI,EAAC,OAAO;gBACZ4B,IAAI,EAAC,MAAM;gBACXK,MAAM,EAAC,SAAS;gBAChBH,QAAQ,EAAEhC,YAAa;gBACvBkC,QAAQ,EAAEtC,OAAQ;gBAClByB,SAAS,EAAC;cAAmP;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9P,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1C,OAAA;YAAAsC,QAAA,eACEtC,OAAA;cACE8C,IAAI,EAAC,QAAQ;cACbI,QAAQ,EAAEtC,OAAQ;cAClByB,SAAS,EAAC,yXAAyX;cAAAC,QAAA,GAElY1B,OAAO,iBACNZ,OAAA;gBAAKqC,SAAS,EAAC,4CAA4C;gBAACe,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAhB,QAAA,gBAC5HtC,OAAA;kBAAQqC,SAAS,EAAC,YAAY;kBAACkB,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrG1C,OAAA;kBAAMqC,SAAS,EAAC,YAAY;kBAACgB,IAAI,EAAC,cAAc;kBAACO,CAAC,EAAC;gBAAiH;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CACN,EACA9B,OAAO,GAAG,gBAAgB,GAAG,UAAU;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA3KIF,UAAU;AAAA4D,EAAA,GAAV5D,UAAU;AA6KhB,eAAeA,UAAU;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}