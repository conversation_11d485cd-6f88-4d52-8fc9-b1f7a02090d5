{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/Task1/frontend/src/components/SignupPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport './Auth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SignupPage = ({\n  onSignupSuccess\n}) => {\n  _s();\n  const [form, setForm] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    photo: null\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      files\n    } = e.target;\n    setForm(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const data = new FormData();\n      Object.entries(form).forEach(([key, value]) => data.append(key, value));\n\n      // Send OTP first (doesn't create user yet)\n      await axios.post('/api/auth/send-otp', data);\n\n      // Navigate to OTP verification with form data\n      onSignupSuccess(form.phone);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Something went wrong. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'red',\n        marginBottom: '10px'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"fullName\",\n      placeholder: \"Full Name\",\n      onChange: handleChange,\n      value: form.fullName,\n      required: true,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"email\",\n      type: \"email\",\n      placeholder: \"Email\",\n      onChange: handleChange,\n      value: form.email,\n      required: true,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"password\",\n      type: \"password\",\n      placeholder: \"Password\",\n      onChange: handleChange,\n      value: form.password,\n      required: true,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"confirmPassword\",\n      type: \"password\",\n      placeholder: \"Confirm Password\",\n      onChange: handleChange,\n      value: form.confirmPassword,\n      required: true,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"phone\",\n      placeholder: \"Phone (e.g., +1234567890)\",\n      onChange: handleChange,\n      value: form.phone,\n      required: true,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      name: \"photo\",\n      type: \"file\",\n      accept: \"image/*\",\n      onChange: handleChange,\n      disabled: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      disabled: loading,\n      children: loading ? 'Sending OTP...' : 'Send OTP'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(SignupPage, \"DW0XRrGBftvfDMfLEL4DehtAtUY=\");\n_c = SignupPage;\nexport default SignupPage;\nvar _c;\n$RefreshReg$(_c, \"SignupPage\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "SignupPage", "onSignupSuccess", "_s", "form", "setForm", "fullName", "email", "password", "confirmPassword", "phone", "photo", "loading", "setLoading", "error", "setError", "handleChange", "e", "name", "value", "files", "target", "prev", "handleSubmit", "preventDefault", "data", "FormData", "Object", "entries", "for<PERSON>ach", "key", "append", "post", "err", "_err$response", "_err$response$data", "response", "onSubmit", "children", "style", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "required", "disabled", "type", "accept", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/Task1/frontend/src/components/SignupPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport './Auth.css';\n\nconst SignupPage = ({ onSignupSuccess }) => {\n  const [form, setForm] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    photo: null,\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleChange = e => {\n    const { name, value, files } = e.target;\n    setForm(prev => ({\n      ...prev,\n      [name]: files ? files[0] : value,\n    }));\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const data = new FormData();\n      Object.entries(form).forEach(([key, value]) => data.append(key, value));\n\n      // Send OTP first (doesn't create user yet)\n      await axios.post('/api/auth/send-otp', data);\n\n      // Navigate to OTP verification with form data\n      onSignupSuccess(form.phone);\n    } catch (err) {\n      setError(err.response?.data?.error || 'Something went wrong. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit}>\n      {error && <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>}\n\n      <input\n        name=\"fullName\"\n        placeholder=\"Full Name\"\n        onChange={handleChange}\n        value={form.fullName}\n        required\n        disabled={loading}\n      />\n      <input\n        name=\"email\"\n        type=\"email\"\n        placeholder=\"Email\"\n        onChange={handleChange}\n        value={form.email}\n        required\n        disabled={loading}\n      />\n      <input\n        name=\"password\"\n        type=\"password\"\n        placeholder=\"Password\"\n        onChange={handleChange}\n        value={form.password}\n        required\n        disabled={loading}\n      />\n      <input\n        name=\"confirmPassword\"\n        type=\"password\"\n        placeholder=\"Confirm Password\"\n        onChange={handleChange}\n        value={form.confirmPassword}\n        required\n        disabled={loading}\n      />\n      <input\n        name=\"phone\"\n        placeholder=\"Phone (e.g., +1234567890)\"\n        onChange={handleChange}\n        value={form.phone}\n        required\n        disabled={loading}\n      />\n      <input\n        name=\"photo\"\n        type=\"file\"\n        accept=\"image/*\"\n        onChange={handleChange}\n        disabled={loading}\n      />\n      <button type=\"submit\" disabled={loading}>\n        {loading ? 'Sending OTP...' : 'Send OTP'}\n      </button>\n    </form>\n  );\n};\n\nexport default SignupPage"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAgB,CAAC,KAAK;EAAAC,EAAA;EAC1C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGR,QAAQ,CAAC;IAC/BS,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMmB,YAAY,GAAGC,CAAC,IAAI;IACxB,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IACvChB,OAAO,CAACiB,IAAI,KAAK;MACf,GAAGA,IAAI;MACP,CAACJ,IAAI,GAAGE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD;IAC7B,CAAC,CAAC,CAAC;IACH;IACA,IAAIL,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMQ,YAAY,GAAG,MAAMN,CAAC,IAAI;IAC9BA,CAAC,CAACO,cAAc,CAAC,CAAC;IAClBX,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMU,IAAI,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC3BC,MAAM,CAACC,OAAO,CAACxB,IAAI,CAAC,CAACyB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEX,KAAK,CAAC,KAAKM,IAAI,CAACM,MAAM,CAACD,GAAG,EAAEX,KAAK,CAAC,CAAC;;MAEvE;MACA,MAAMrB,KAAK,CAACkC,IAAI,CAAC,oBAAoB,EAAEP,IAAI,CAAC;;MAE5C;MACAvB,eAAe,CAACE,IAAI,CAACM,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOuB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZpB,QAAQ,CAAC,EAAAmB,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcT,IAAI,cAAAU,kBAAA,uBAAlBA,kBAAA,CAAoBrB,KAAK,KAAI,yCAAyC,CAAC;IAClF,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEb,OAAA;IAAMqC,QAAQ,EAAEd,YAAa;IAAAe,QAAA,GAC1BxB,KAAK,iBAAId,OAAA;MAAKuC,KAAK,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAH,QAAA,EAAExB;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE3E7C,OAAA;MACEkB,IAAI,EAAC,UAAU;MACf4B,WAAW,EAAC,WAAW;MACvBC,QAAQ,EAAE/B,YAAa;MACvBG,KAAK,EAAEf,IAAI,CAACE,QAAS;MACrB0C,QAAQ;MACRC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MACEkB,IAAI,EAAC,OAAO;MACZgC,IAAI,EAAC,OAAO;MACZJ,WAAW,EAAC,OAAO;MACnBC,QAAQ,EAAE/B,YAAa;MACvBG,KAAK,EAAEf,IAAI,CAACG,KAAM;MAClByC,QAAQ;MACRC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MACEkB,IAAI,EAAC,UAAU;MACfgC,IAAI,EAAC,UAAU;MACfJ,WAAW,EAAC,UAAU;MACtBC,QAAQ,EAAE/B,YAAa;MACvBG,KAAK,EAAEf,IAAI,CAACI,QAAS;MACrBwC,QAAQ;MACRC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MACEkB,IAAI,EAAC,iBAAiB;MACtBgC,IAAI,EAAC,UAAU;MACfJ,WAAW,EAAC,kBAAkB;MAC9BC,QAAQ,EAAE/B,YAAa;MACvBG,KAAK,EAAEf,IAAI,CAACK,eAAgB;MAC5BuC,QAAQ;MACRC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MACEkB,IAAI,EAAC,OAAO;MACZ4B,WAAW,EAAC,2BAA2B;MACvCC,QAAQ,EAAE/B,YAAa;MACvBG,KAAK,EAAEf,IAAI,CAACM,KAAM;MAClBsC,QAAQ;MACRC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MACEkB,IAAI,EAAC,OAAO;MACZgC,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,SAAS;MAChBJ,QAAQ,EAAE/B,YAAa;MACvBiC,QAAQ,EAAErC;IAAQ;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eACF7C,OAAA;MAAQkD,IAAI,EAAC,QAAQ;MAACD,QAAQ,EAAErC,OAAQ;MAAA0B,QAAA,EACrC1B,OAAO,GAAG,gBAAgB,GAAG;IAAU;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAAC1C,EAAA,CAtGIF,UAAU;AAAAmD,EAAA,GAAVnD,UAAU;AAwGhB,eAAeA,UAAU;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}